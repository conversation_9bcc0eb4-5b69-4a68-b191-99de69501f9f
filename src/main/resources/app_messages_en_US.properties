# APP-SPECIFIC MESSAGES - ENGLISH (US)
# This bundle contains messages related to specific applications

# App Names
app.name.purchasing=Purchasing
app.name.finance=Finance
app.name.budgets=Budgets

# App Descriptions
app.description.purchasing=Purchasing application for managing procurement processes
app.description.finance=Finance application for managing financial operations
app.description.budgets=Budgets application for managing budget planning and control

# Common App Messages
app.welcome.budgets=Welcome to Budgets App!
app.welcome.finance=Welcome to Finance App!
app.welcome.purchasing=Welcome to Purchasing App!

# Budgets App
app.budgets.plans=Budget Plans
app.budgets.plans.description=Create budget plans
app.budgets.expenses=Expenses
app.budgets.expenses.description=Track expenses
app.budgets.goals=Goals
app.budgets.goals.description=Set budget goals
app.budgets.summary=Budget Summary
app.budgets.nodata=No budget data
app.budgets.nodata.description=Start creating budgets to see summary here.

# Finance App
app.finance.accounts=Accounts
app.finance.accounts.description=Manage accounts
app.finance.invoices=Invoices
app.finance.invoices.description=Manage invoices
app.finance.reports=Reports
app.finance.reports.description=Financial reports
app.finance.overview=Financial Overview
app.finance.nodata=No financial data
app.finance.nodata.description=Start using the finance features to see data here.

# Purchasing App
app.purchasing.dashboard.overview=Dashboard Overview
app.purchasing.orders=Purchase Orders
app.purchasing.orders.description=Manage purchase orders
app.purchasing.suppliers=Suppliers
app.purchasing.suppliers.description=Manage suppliers
app.purchasing.inventory=Inventory
app.purchasing.inventory.description=Track inventory
app.purchasing.activities=Recent Activities
app.purchasing.noactivities=No recent activities
app.purchasing.noactivities.description=Start using the purchasing features to see activities here.

# Purchasing Admin
app.purchasing.admin.title=Purchasing Administration
app.purchasing.admin.panel=Admin Panel (Admin Only Access)
app.purchasing.admin.panel.description=This is the administration panel for the Purchasing application. Only users with ADMIN role can access this page.
app.purchasing.admin.system=System Management
app.purchasing.admin.system.description=Manage system-wide purchasing settings and data.
app.purchasing.admin.users=User Administration
app.purchasing.admin.users.description=Manage user roles and permissions across the purchasing module.
app.purchasing.admin.analytics=Analytics & Reports
app.purchasing.admin.analytics.description=Access advanced analytics and system reports.
app.purchasing.admin.access.info=Administrator Access Information
app.purchasing.admin.access.level=Full administrative control
app.purchasing.admin.capabilities=System management, user administration, data access, security configuration

# Purchasing Settings
app.purchasing.settings.title=Purchasing Settings
app.purchasing.settings.panel=Settings Page (Moderator+ Access)
app.purchasing.settings.panel.description=This is the settings page for the Purchasing application. Only users with MODERATOR or ADMIN roles can access this page.
app.purchasing.settings.permissions=User Permissions
app.purchasing.settings.permissions.description=Manage user access and permissions for the purchasing module.
app.purchasing.settings.config=App Configuration
app.purchasing.settings.config.description=Configure purchasing workflows and approval processes.
app.purchasing.settings.access.info=Access Information
app.purchasing.settings.access.level=Settings and configuration management

# Common Actions
app.action.manage=Manage
app.action.configure=Configure
app.action.view=View

# Common Labels
app.label.role=Your Role:
app.label.access.level=Access Level:
app.label.capabilities=Capabilities:
