<!DOCTYPE html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/web/thymeleaf/layout"
      layout:decorate="~{layout}">

<head>
    <title th:text="#{app.name.purchasing} + ' - Admin'">Purchasing - Admin</title>
</head>

<body>

<div layout:fragment="content">
    <div class="col-12">
        <!-- App Header with Breadcrumb -->
        <div th:replace="~{fragments/breadcrumb :: app-header}"></div>
        
        <!-- App Navigation -->
        <div th:replace="~{fragments/breadcrumb :: app-navigation}"></div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="ti ti-shield me-2"></i>
                    <span th:text="#{app.purchasing.admin.title}">Purchasing Administration</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-danger" role="alert">
                    <h5 class="alert-heading">
                        <i class="ti ti-shield-lock me-2"></i>
                        <span th:text="#{app.purchasing.admin.panel}">Admin Panel (Admin Only Access)</span>
                    </h5>
                    <p class="mb-0" th:text="#{app.purchasing.admin.panel.description}">
                        This is the administration panel for the Purchasing application.
                        Only users with ADMIN role can access this page.
                    </p>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h6 class="card-title text-white">
                                    <i class="ti ti-database me-2"></i>
                                    <span th:text="#{app.purchasing.admin.system}">System Management</span>
                                </h6>
                                <p class="card-text text-white-50" th:text="#{app.purchasing.admin.system.description}">Manage system-wide purchasing settings and data.</p>
                                <button class="btn btn-outline-light btn-sm" th:text="#{app.action.manage}">Manage</button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card bg-warning text-dark">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ti ti-users-group me-2"></i>
                                    <span th:text="#{app.purchasing.admin.users}">User Administration</span>
                                </h6>
                                <p class="card-text" th:text="#{app.purchasing.admin.users.description}">Manage user roles and permissions across the purchasing module.</p>
                                <button class="btn btn-outline-dark btn-sm" th:text="#{app.action.manage}">Manage</button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h6 class="card-title text-white">
                                    <i class="ti ti-chart-bar me-2"></i>
                                    <span th:text="#{app.purchasing.admin.analytics}">Analytics & Reports</span>
                                </h6>
                                <p class="card-text text-white-50" th:text="#{app.purchasing.admin.analytics.description}">Access advanced analytics and system reports.</p>
                                <button class="btn btn-outline-light btn-sm" th:text="#{app.action.view}">View</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h6 class="card-title mb-0">
                                    <i class="ti ti-shield-check me-2"></i>
                                    <span th:text="#{app.purchasing.admin.access.info}">Administrator Access Information</span>
                                </h6>
                            </div>
                            <div class="card-body" style="padding: 20px;">
                                <p class="mb-2"><strong th:text="#{app.label.role}">Your Role:</strong>
                                    <span class="badge bg-danger" th:text="${userRoleLevel}">ADMIN</span>
                                </p>
                                <p class="mb-2"><strong th:text="#{app.label.access.level}">Access Level:</strong> <span th:text="#{app.purchasing.admin.access.level}">Full administrative control</span></p>
                                <p class="mb-0"><strong th:text="#{app.label.capabilities}">Capabilities:</strong> <span th:text="#{app.purchasing.admin.capabilities}">System management, user administration, data access, security configuration</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
