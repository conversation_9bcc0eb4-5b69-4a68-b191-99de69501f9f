<!DOCTYPE html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/web/thymeleaf/layout"
      layout:decorate="~{layout}">

<head>
    <title th:text="#{app.name.purchasing} + ' - Settings'">Purchasing - Settings</title>
</head>

<body>

<div layout:fragment="content">
    <div class="col-12">
        <!-- App Header with Breadcrumb -->
        <div th:replace="~{fragments/breadcrumb :: app-header}"></div>
        
        <!-- App Navigation -->
        <div th:replace="~{fragments/breadcrumb :: app-navigation}"></div>
        
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="ti ti-settings me-2"></i>
                    <span th:text="#{app.purchasing.settings.title}">Purchasing Settings</span>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <h5 class="alert-heading">
                        <i class="ti ti-settings me-2"></i>
                        <span th:text="#{app.purchasing.settings.panel}">Settings Page (Moderator+ Access)</span>
                    </h5>
                    <p class="mb-0" th:text="#{app.purchasing.settings.panel.description}">
                        This is the settings page for the Purchasing application.
                        Only users with MODERATOR or ADMIN roles can access this page.
                    </p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ti ti-user-cog me-2"></i>
                                    <span th:text="#{app.purchasing.settings.permissions}">User Permissions</span>
                                </h6>
                                <p class="card-text" th:text="#{app.purchasing.settings.permissions.description}">Manage user access and permissions for the purchasing module.</p>
                                <button class="btn btn-outline-primary btn-sm" th:text="#{app.action.configure}">Configure</button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="ti ti-settings-cog me-2"></i>
                                    <span th:text="#{app.purchasing.settings.config}">App Configuration</span>
                                </h6>
                                <p class="card-text" th:text="#{app.purchasing.settings.config.description}">Configure purchasing workflows and approval processes.</p>
                                <button class="btn btn-outline-primary btn-sm" th:text="#{app.action.configure}">Configure</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="card-title mb-0">
                                    <i class="ti ti-shield-check me-2"></i>
                                    <span th:text="#{app.purchasing.settings.access.info}">Access Information</span>
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="mb-2"><strong th:text="#{app.label.role}">Your Role:</strong>
                                    <span class="badge bg-warning" th:text="${userRoleLevel}">MODERATOR</span>
                                </p>
                                <p class="mb-0"><strong th:text="#{app.label.access.level}">Access Level:</strong> <span th:text="#{app.purchasing.settings.access.level}">Settings and configuration management</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
