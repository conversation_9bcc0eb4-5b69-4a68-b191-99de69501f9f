<!DOCTYPE html>
<html th:lang="${#locale.language}" xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/web/thymeleaf/layout"
      layout:decorate="~{layout}">

<head>
    <title th:text="#{app.name.finance}">Finance</title>
</head>

<body>

<div layout:fragment="content">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex align-items-center justify-content-between">
                <h4 class="card-title mb-0">
                    <i class="ti ti-currency-dollar me-2"></i>
                    <span th:text="#{app.name.finance}">Finance</span>
                </h4>
                <div class="badge bg-label-primary" th:text="${userRoleLevel}">USER</div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-success" role="alert">
                            <h5 class="alert-heading">
                                <i class="ti ti-info-circle me-2"></i>
                                <span th:text="#{app.welcome.finance}">Welcome to Finance App!</span>
                            </h5>
                            <p class="mb-0" th:text="#{app.description.finance}">
                                Finance application for managing financial processes
                            </p>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ti ti-currency-dollar ti-lg"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="card-title text-white mb-1" th:text="#{app.finance.accounts}">Accounts</h6>
                                        <p class="card-text text-white-50 mb-0" th:text="#{app.finance.accounts.description}">Manage accounts</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ti ti-file-invoice ti-lg"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="card-title text-white mb-1" th:text="#{app.finance.invoices}">Invoices</h6>
                                        <p class="card-text text-white-50 mb-0" th:text="#{app.finance.invoices.description}">Manage invoices</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ti ti-chart-line ti-lg"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="card-title text-white mb-1" th:text="#{app.finance.reports}">Reports</h6>
                                        <p class="card-text text-white-50 mb-0" th:text="#{app.finance.reports.description}">Financial reports</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0" th:text="#{app.finance.overview}">Financial Overview</h5>
                            </div>
                            <div class="card-body">
                                <div class="text-center py-4">
                                    <i class="ti ti-currency-dollar ti-3x text-muted mb-3"></i>
                                    <h6 class="text-muted" th:text="#{app.finance.nodata}">No financial data</h6>
                                    <p class="text-muted mb-0" th:text="#{app.finance.nodata.description}">Start using the finance features to see data here.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
