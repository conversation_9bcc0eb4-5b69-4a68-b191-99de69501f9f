package ag.fuel.jobify.common.util;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

public final class Constants {

    private Constants() {
        // No need to instantiate the class, we can hide its constructor
    }

    /* Global Strings */
    public static final String DASH = "-";
    public static final String HASHTAG = "#";
    public static final String EQUALS = "=";

    /* Avatar Images Folder */
    public static final String IMAGES_UPLOAD_FOLDER = "/images/";

    /* Modal Icons */
    public static final String LANG_PT_BR = "pt-BR";
    public static final String ICON_SUCCESS = "success";
    public static final String ICON_ERROR = "error";
    public static final String ICON_WARNING = "warning";

    /* Notifications */
    public static final String NOTIF_TYPE_SYSTEM = "SYSTEM";
    public static final String NOTIF_TYPE_USER = "USER";

    /* GeoLocation and Agent */
    public static final String GEO_COUNTRY_CODE = "CountryCode";
    public static final String GEO_CITY = "City";
    public static final String GEO_COUNTRY = "Country";

    /* Global Time/Date */
    public static final ZoneId BRASIL_TIME_ZONE = ZoneId.of("America/Sao_Paulo");
    public static final DateTimeFormatter BRASIL_DATETIME_FORMAT_HHMMSS = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
    public static final DateTimeFormatter BRASIL_DATETIME_FORMAT_HHMM = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm");
}
