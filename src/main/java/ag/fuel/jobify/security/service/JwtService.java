package ag.fuel.jobify.security.service;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;

import java.security.Key;
import java.security.SecureRandom;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

@Service
public class JwtService {
    private static final Logger LOGGER = LoggerFactory.getLogger(JwtService.class);

    private final SecureRandom secureRandom;

    @Value("${security.jwt.secret-key}")
    private String secretKey;

    @Value("${security.jwt.expiration-time}")
    private long jwtExpiration;

    @Value("${security.jwt.refresh-expiration-time:86400000}") // Default to 24 hours if not specified
    private long refreshExpiration;

    @Value("${security.jwt.name}")
    private String jwtName;

    @Value("${application.base-url}")
    private String issuer;

    public JwtService(SecureRandom secureRandom) {
        this.secureRandom = secureRandom;
    }

    @PostConstruct
    public void init() {
        LOGGER.info("JWT Service initialized with HMAC-SHA256 signing");
        // Validate that the secret key is properly configured
        if (secretKey == null || secretKey.trim().isEmpty()) {
            LOGGER.error("JWT secret key is not configured. Please set security.jwt.secret-key property.");
            throw new IllegalStateException("JWT secret key must be configured");
        }
    }

    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    public String generateToken(UserDetails userDetails) {
        return generateToken(new HashMap<>(), userDetails);
    }

    public String generateToken(Map<String, Object> extraClaims, UserDetails userDetails) {
        return buildToken(extraClaims, userDetails, jwtExpiration);
    }

    public String generateRefreshToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("token_type", "refresh");
        return buildToken(claims, userDetails, refreshExpiration);
    }

    public long getExpirationTime() {
        return jwtExpiration;
    }

    public long getRefreshExpirationTime() {
        return refreshExpiration;
    }

    public String getJwtName() {
        return jwtName;
    }

    private String buildToken(
            Map<String, Object> extraClaims,
            UserDetails userDetails,
            long expiration
    ) {
        // Add standard claims - use optimized SecureRandom for better performance
        String tokenId = generateSecureTokenId();
        Date issuedAt = new Date(System.currentTimeMillis());
        Date expiryDate = new Date(System.currentTimeMillis() + expiration);

        // Set token type to "access" by default if not specified
        if (!extraClaims.containsKey("token_type")) {
            extraClaims.put("token_type", "access");
        }

        // Build the token
        var tokenBuilder = Jwts.builder()
                .setClaims(extraClaims)
                .setSubject(userDetails.getUsername())
                .setIssuedAt(issuedAt)
                .setExpiration(expiryDate)
                .setId(tokenId)
                .setIssuer(issuer);

        // Use HMAC-SHA256 consistently for all tokens
        return tokenBuilder.signWith(getSignInKey(), SignatureAlgorithm.HS256).compact();
    }

    public boolean isTokenValid(String token, UserDetails userDetails) {
        try {
            final String username = extractUsername(token);
            return (username.equals(userDetails.getUsername())) && !isTokenExpired(token);
        } catch (Exception e) {
            LOGGER.error("Error validating token: {}", e.getMessage());
            return false;
        }
    }

    public boolean isAccessToken(String token) {
        try {
            String tokenType = extractClaim(token, claims -> claims.get("token_type", String.class));
            return "access".equals(tokenType);
        } catch (Exception e) {
            LOGGER.error("Error checking token type: {}", e.getMessage());
            return false;
        }
    }

    public boolean isRefreshToken(String token) {
        try {
            String tokenType = extractClaim(token, claims -> claims.get("token_type", String.class));
            return "refresh".equals(tokenType);
        } catch (Exception e) {
            LOGGER.error("Error checking token type: {}", e.getMessage());
            return false;
        }
    }

    private boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    private Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    private Claims extractAllClaims(String token) {
        // Use HMAC-SHA256 consistently for all token parsing
        return Jwts
                .parserBuilder()
                .setSigningKey(getSignInKey())
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    private Key getSignInKey() {
        byte[] keyBytes = Decoders.BASE64.decode(secretKey);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * Generates a secure token ID using the optimized SecureRandom instance.
     * This avoids the performance overhead of UUID.randomUUID() which can be slow.
     *
     * @return A secure random token ID
     */
    private String generateSecureTokenId() {
        // Generate 16 random bytes (128 bits) for a secure token ID
        byte[] randomBytes = new byte[16];
        secureRandom.nextBytes(randomBytes);

        // Convert to UUID format for compatibility
        long mostSigBits = 0;
        long leastSigBits = 0;

        for (int i = 0; i < 8; i++) {
            mostSigBits = (mostSigBits << 8) | (randomBytes[i] & 0xff);
        }
        for (int i = 8; i < 16; i++) {
            leastSigBits = (leastSigBits << 8) | (randomBytes[i] & 0xff);
        }

        UUID uuid = new UUID(mostSigBits, leastSigBits);
        return uuid.toString();
    }

    public String getStoredCookieValue(HttpServletRequest request) {
        String t = null;
        if (request.getCookies() != null) {
            Cookie[] rc = request.getCookies();
            for (int i = 0; i < rc.length; i++) {
                if (rc[i].getName().equals(this.getJwtName()) == true) {
                    t = rc[i].getValue().toString();
                }
            }
        }
        return t;
    }
}
