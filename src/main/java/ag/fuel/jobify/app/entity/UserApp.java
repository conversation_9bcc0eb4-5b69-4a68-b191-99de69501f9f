package ag.fuel.jobify.app.entity;

import ag.fuel.jobify.auth.entity.ERole;
import ag.fuel.jobify.user.entity.User;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

/**
 * Entity representing the relationship between a user and an app.
 * This entity stores which apps are enabled for each user and their role level for that app.
 */
@Entity
@Table(name = "job_user_apps")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class UserApp {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "app_id", nullable = false)
    private App app;

    @Enumerated(EnumType.STRING)
    @Column(name = "role_level", nullable = false)
    private ERole roleLevel;

    @Column(name = "enabled", nullable = false)
    private boolean enabled = true;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    public UserApp(User user, App app, ERole roleLevel) {
        this.user = user;
        this.app = app;
        this.roleLevel = roleLevel;
        this.enabled = true;
    }
}