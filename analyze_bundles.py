#!/usr/bin/env python3
"""
<PERSON>ript to analyze resource bundle files for duplicates and unused keys
"""

import re
import os
from collections import defaultdict

def read_properties_file(file_path):
    """Read a properties file and return a dict of key-value pairs"""
    properties = {}
    if not os.path.exists(file_path):
        return properties
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            # Skip comments and empty lines
            if not line or line.startswith('#'):
                continue
            
            # Split on first = sign
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                properties[key] = {
                    'value': value,
                    'line': line_num,
                    'file': file_path
                }
    
    return properties

def find_used_keys_in_templates():
    """Find all message keys used in templates"""
    used_keys = set()
    template_dir = "src/main/resources/templates"
    
    # Pattern to match #{key} in templates
    pattern = r'#\{([^}]+)\}'
    
    for root, dirs, files in os.walk(template_dir):
        for file in files:
            if file.endswith('.html'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        matches = re.findall(pattern, content)
                        for match in matches:
                            # Handle dynamic keys like #{'app.name.' + ${app.appName.toLowerCase()}}
                            if not match.startswith("'") and not '+' in match:
                                used_keys.add(match)
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return used_keys

def find_used_keys_in_java():
    """Find all message keys used in Java files"""
    used_keys = set()
    java_dir = "src/main/java"
    
    # Pattern to match messageSource.getMessage("key", ...)
    pattern = r'messageSource\.getMessage\s*\(\s*"([^"]+)"'
    
    for root, dirs, files in os.walk(java_dir):
        for file in files:
            if file.endswith('.java'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        matches = re.findall(pattern, content)
                        used_keys.update(matches)
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return used_keys

def analyze_bundles():
    """Main analysis function"""
    # Read all bundle files
    bundle_files = [
        "src/main/resources/messages.properties",
        "src/main/resources/messages_en_US.properties", 
        "src/main/resources/messages_pt_BR.properties",
        "src/main/resources/app_messages.properties",
        "src/main/resources/app_messages_en_US.properties",
        "src/main/resources/app_messages_pt_BR.properties"
    ]
    
    all_keys = {}
    key_locations = defaultdict(list)
    
    # Read all properties
    for bundle_file in bundle_files:
        properties = read_properties_file(bundle_file)
        for key, info in properties.items():
            if key in all_keys:
                # Duplicate found
                key_locations[key].append(info)
            else:
                all_keys[key] = info
                key_locations[key].append(info)
    
    # Find used keys
    template_keys = find_used_keys_in_templates()
    java_keys = find_used_keys_in_java()
    all_used_keys = template_keys.union(java_keys)
    
    # Find duplicates
    duplicates = {k: v for k, v in key_locations.items() if len(v) > 1}
    
    # Find unused keys
    unused_keys = set(all_keys.keys()) - all_used_keys
    
    return {
        'duplicates': duplicates,
        'unused_keys': unused_keys,
        'all_keys': all_keys,
        'template_keys': template_keys,
        'java_keys': java_keys,
        'all_used_keys': all_used_keys
    }

if __name__ == "__main__":
    results = analyze_bundles()
    
    print("=== DUPLICATE KEYS ANALYSIS ===")
    if results['duplicates']:
        for key, locations in results['duplicates'].items():
            print(f"\nDuplicate key: {key}")
            for loc in locations:
                print(f"  - {loc['file']}:{loc['line']} = {loc['value']}")
    else:
        print("No duplicate keys found.")
    
    print(f"\n=== UNUSED KEYS ANALYSIS ===")
    print(f"Total keys: {len(results['all_keys'])}")
    print(f"Used in templates: {len(results['template_keys'])}")
    print(f"Used in Java: {len(results['java_keys'])}")
    print(f"Total used: {len(results['all_used_keys'])}")
    print(f"Unused: {len(results['unused_keys'])}")
    
    if results['unused_keys']:
        print("\nUnused keys:")
        for key in sorted(results['unused_keys']):
            info = results['all_keys'][key]
            print(f"  - {key} ({info['file']}:{info['line']})")
